// Security Scanner Pro - Enhanced JavaScript

class SecurityScannerApp {
    constructor() {
        this.currentScanId = null;
        this.pollingInterval = null;
        this.pollDelay = 2000; // 2 segundos
        this.progressValue = 0;
        this.tools = ['whois', 'dig', 'theharvester', 'assetfinder', 'nuclei'];
        this.toolsCompleted = 0;
        this.scanCount = 0;
        this.currentTheme = 'light';
        this.notifications = [];

        this.init();
    }
    
    init() {
        this.loadTheme();
        this.bindEvents();
        this.initializeTooltips();
        this.showInitialMessage();
        this.updateScanCounter();
        console.log('Security Scanner Pro initialized');
    }

    bindEvents() {
        // Evento del formulario de escaneo
        const scanForm = document.getElementById('scanForm');
        if (scanForm) {
            scanForm.addEventListener('submit', (e) => this.handleScanSubmit(e));
        }

        // Botón de descarga de reporte
        const downloadBtn = document.getElementById('downloadReportBtn');
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadReport());
        }

        // Validación en tiempo real del input
        const targetInput = document.getElementById('targetInput');
        if (targetInput) {
            targetInput.addEventListener('input', (e) => this.validateInput(e));
            targetInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
        }

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        // Scan options
        const deepScan = document.getElementById('deepScan');
        const fastMode = document.getElementById('fastMode');
        if (deepScan && fastMode) {
            deepScan.addEventListener('change', (e) => {
                if (e.target.checked) fastMode.checked = false;
            });
            fastMode.addEventListener('change', (e) => {
                if (e.target.checked) deepScan.checked = false;
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleGlobalKeyDown(e));

        // Window events
        window.addEventListener('beforeunload', () => this.cleanup());
        window.addEventListener('focus', () => this.handleWindowFocus());
    }
    
    // Theme Management
    loadTheme() {
        const savedTheme = localStorage.getItem('securityScannerTheme') || 'light';
        this.currentTheme = savedTheme;
        document.documentElement.setAttribute('data-bs-theme', savedTheme);
        this.updateThemeIcon();
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        document.documentElement.setAttribute('data-bs-theme', this.currentTheme);
        localStorage.setItem('securityScannerTheme', this.currentTheme);
        this.updateThemeIcon();
        this.showNotification(`Tema cambiado a ${this.currentTheme === 'light' ? 'claro' : 'oscuro'}`, 'info');
    }

    updateThemeIcon() {
        const themeIcon = document.getElementById('themeIcon');
        if (themeIcon) {
            themeIcon.className = this.currentTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }

    // Tooltips
    initializeTooltips() {
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    // Scan Counter
    updateScanCounter() {
        const scanCountElement = document.getElementById('scanCount');
        if (scanCountElement) {
            scanCountElement.textContent = this.scanCount;
        }
    }

    // Enhanced Input Validation
    validateInput(event) {
        const input = event.target;
        const value = input.value.trim();

        // Expresión regular para validar dominios
        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.[a-zA-Z]{2,}$/;

        if (value && !domainRegex.test(value)) {
            input.classList.add('is-invalid');
            input.classList.remove('is-valid');
            this.showInputFeedback(input, 'Formato de dominio inválido', 'error');
        } else if (value) {
            input.classList.add('is-valid');
            input.classList.remove('is-invalid');
            this.showInputFeedback(input, 'Dominio válido', 'success');
        } else {
            input.classList.remove('is-invalid', 'is-valid');
            this.clearInputFeedback(input);
        }
    }

    showInputFeedback(input, message, type) {
        let feedback = input.parentNode.querySelector('.input-feedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.className = 'input-feedback small mt-1';
            input.parentNode.appendChild(feedback);
        }

        feedback.textContent = message;
        feedback.className = `input-feedback small mt-1 text-${type === 'error' ? 'danger' : 'success'}`;
    }

    clearInputFeedback(input) {
        const feedback = input.parentNode.querySelector('.input-feedback');
        if (feedback) {
            feedback.remove();
        }
    }

    // Keyboard Handlers
    handleKeyDown(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
            const form = event.target.closest('form');
            if (form) {
                form.dispatchEvent(new Event('submit'));
            }
        }
    }

    handleGlobalKeyDown(event) {
        // Ctrl/Cmd + Enter para iniciar escaneo
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            const scanForm = document.getElementById('scanForm');
            if (scanForm) {
                scanForm.dispatchEvent(new Event('submit'));
            }
        }

        // Ctrl/Cmd + D para descargar reporte
        if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
            event.preventDefault();
            const downloadBtn = document.getElementById('downloadReportBtn');
            if (downloadBtn && downloadBtn.style.display !== 'none') {
                this.downloadReport();
            }
        }

        // Ctrl/Cmd + Shift + T para cambiar tema
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
            event.preventDefault();
            this.toggleTheme();
        }
    }

    // Window Focus Handler
    handleWindowFocus() {
        if (this.currentScanId && this.pollingInterval) {
            // Refresh scan status when window regains focus
            this.fetchScanStatus();
        }
    }

    // Cleanup
    cleanup() {
        this.stopPolling();
    }
    
    async handleScanSubmit(event) {
        event.preventDefault();
        
        const targetInput = document.getElementById('targetInput');
        const target = targetInput.value.trim();
        
        if (!target) {
            this.showAlert('Por favor ingresa un dominio válido', 'warning');
            return;
        }
        
        try {
            await this.startScan(target);
        } catch (error) {
            console.error('Error starting scan:', error);
            this.showAlert('Error al iniciar el análisis: ' + error.message, 'danger');
        }
    }
    
    // Enhanced Notification System
    showNotification(message, type = 'info', duration = 5000) {
        const notification = {
            id: Date.now(),
            message,
            type,
            timestamp: new Date()
        };

        this.notifications.push(notification);
        this.displayNotification(notification);

        // Auto remove after duration
        setTimeout(() => {
            this.removeNotification(notification.id);
        }, duration);
    }

    displayNotification(notification) {
        const container = document.querySelector('.container-fluid');
        if (!container) return;

        const alertHtml = `
            <div class="alert alert-${notification.type} alert-dismissible fade show notification-toast"
                 role="alert" data-notification-id="${notification.id}">
                <div class="d-flex align-items-center">
                    <i class="fas ${this.getNotificationIcon(notification.type)} me-2"></i>
                    <div class="flex-grow-1">
                        <strong>${this.getNotificationTitle(notification.type)}</strong>
                        <div>${notification.message}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        `;

        const alertDiv = document.createElement('div');
        alertDiv.innerHTML = alertHtml;
        alertDiv.classList.add('notification-container');

        container.insertBefore(alertDiv.firstElementChild, container.firstChild);
    }

    removeNotification(id) {
        const notification = document.querySelector(`[data-notification-id="${id}"]`);
        if (notification) {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }
        this.notifications = this.notifications.filter(n => n.id !== id);
    }

    getNotificationIcon(type) {
        const icons = {
            'success': 'fa-check-circle',
            'danger': 'fa-exclamation-circle',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle'
        };
        return icons[type] || 'fa-info-circle';
    }

    getNotificationTitle(type) {
        const titles = {
            'success': 'Éxito',
            'danger': 'Error',
            'warning': 'Advertencia',
            'info': 'Información'
        };
        return titles[type] || 'Información';
    }

    async startScan(target) {
        const startBtn = document.getElementById('startScanBtn');
        const btnText = startBtn.querySelector('.btn-text');
        const btnLoader = startBtn.querySelector('.btn-loader');
        const scanInfo = document.getElementById('scanInfo');
        const resultsSection = document.getElementById('resultsSection');
        const initialMessage = document.getElementById('initialMessage');

        // Enhanced button loading state
        startBtn.disabled = true;
        btnText.classList.add('d-none');
        btnLoader.classList.remove('d-none');

        try {
            const response = await fetch('/scan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ target: target })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.error || 'Error al iniciar el escaneo');
            }

            this.currentScanId = data.scan_id;
            this.scanCount++;
            this.updateScanCounter();

            // Update scan info with enhanced display
            const scanIdElement = document.getElementById('scanId');
            if (scanIdElement) {
                scanIdElement.textContent = `ID: ${data.scan_id.substring(0, 8)}...`;
            }

            // Show scan info with animation
            if (scanInfo) {
                scanInfo.style.display = 'block';
                scanInfo.classList.add('fade-in');
            }

            // Hide initial message and show results with animation
            if (initialMessage) {
                initialMessage.style.display = 'none';
            }
            if (resultsSection) {
                resultsSection.style.display = 'block';
                resultsSection.classList.add('fade-in');
            }

            // Initialize tool status indicators
            this.initializeToolStatus();

            // Start polling for results
            this.startPolling();

            this.showNotification(`Análisis iniciado para ${target}`, 'success');

        } catch (error) {
            console.error('Error:', error);
            this.showNotification(error.message, 'danger');

            // Reset button state
            btnText.classList.remove('d-none');
            btnLoader.classList.add('d-none');
            startBtn.disabled = false;
        }
    }

    initializeToolStatus() {
        const toolStatusItems = document.querySelectorAll('.tool-status-item');
        toolStatusItems.forEach(item => {
            item.removeAttribute('data-status');
            const indicator = item.querySelector('.status-indicator');
            if (indicator) {
                indicator.style.background = 'var(--gray-300)';
            }
        });
    }
    
    startPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
        }
        
        this.pollingInterval = setInterval(() => {
            this.fetchScanStatus();
        }, this.pollDelay);
        
        // Primera llamada inmediata
        this.fetchScanStatus();
    }
    
    async fetchScanStatus() {
        if (!this.currentScanId) return;
        
        try {
            const response = await fetch(`/status/${this.currentScanId}`);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || 'Error al obtener estado del escaneo');
            }
            
            this.updateUI(data);
            
            // Si el escaneo está completo, detener polling
            if (data.status === 'completed' || data.status === 'completed_with_errors' || data.status === 'error') {
                this.stopPolling();
                this.onScanCompleted(data);
            }
            
        } catch (error) {
            console.error('Error fetching scan status:', error);
            this.showAlert('Error al obtener estado del análisis: ' + error.message, 'warning');
        }
    }
    
    updateUI(data) {
        this.updateProgress(data);
        this.updateVulnerabilities(data.vulnerabilities || []);
        this.updateDNSInfo(data.tools_results);
        this.updateWhoisInfo(data.tools_results);
        this.updateSubdomains(data.tools_results);
        this.updateEmails(data.tools_results);
        this.updateRecommendations(data.recommendations || []);
    }
    
    updateProgress(data) {
        const progressBar = document.getElementById('progressBar');
        const currentTool = document.getElementById('currentTool');
        const progressPercent = document.getElementById('progressPercent');

        // Calculate progress based on completed tools
        let completed = 0;
        const toolsResults = data.tools_results || {};

        // Update individual tool status indicators
        this.tools.forEach(tool => {
            const toolItem = document.querySelector(`[data-tool="${tool}"]`);
            const toolResult = toolsResults[tool];

            if (toolResult) {
                const status = toolResult.status;
                if (toolItem) {
                    toolItem.setAttribute('data-status', status);
                }

                if (status === 'completed' || status === 'error' || status === 'timeout') {
                    completed++;
                }
            }
        });

        const progress = Math.round((completed / this.tools.length) * 100);

        // Update progress bar with smooth animation
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        }

        // Update progress percentage
        if (progressPercent) {
            progressPercent.textContent = `${progress}%`;
        }

        // Update current tool status with enhanced messaging
        if (currentTool) {
            if (data.status === 'completed') {
                currentTool.innerHTML = '<i class="fas fa-check text-success me-1"></i>Análisis completado exitosamente';
            } else if (data.status === 'error') {
                currentTool.innerHTML = '<i class="fas fa-times text-danger me-1"></i>Error en el análisis';
            } else if (data.status === 'completed_with_errors') {
                currentTool.innerHTML = '<i class="fas fa-exclamation-triangle text-warning me-1"></i>Completado con advertencias';
            } else {
                const runningTool = this.tools.find(tool => {
                    const result = toolsResults[tool];
                    return result && result.status === 'running';
                });

                if (runningTool) {
                    const toolNames = {
                        'whois': 'Whois',
                        'dig': 'DNS',
                        'theharvester': 'theHarvester',
                        'assetfinder': 'Assetfinder',
                        'nuclei': 'Nuclei'
                    };
                    currentTool.innerHTML = `<i class="fas fa-cog fa-spin text-primary me-1"></i>Ejecutando: ${toolNames[runningTool]}`;
                } else {
                    currentTool.innerHTML = `<i class="fas fa-clock text-info me-1"></i>Progreso: ${completed}/${this.tools.length} herramientas`;
                }
            }
        }

        // Update vulnerability badge
        this.updateVulnerabilityBadge(data.vulnerabilities || []);
    }

    updateVulnerabilityBadge(vulnerabilities) {
        const vulnBadge = document.getElementById('vulnBadge');
        if (!vulnBadge) return;

        const vulnCount = vulnerabilities.length;
        const criticalCount = vulnerabilities.filter(v => v.severity === 'critical').length;
        const highCount = vulnerabilities.filter(v => v.severity === 'high').length;

        let badgeHtml = '';
        if (vulnCount === 0) {
            badgeHtml = '<span class="badge bg-success">Sin vulnerabilidades</span>';
        } else {
            const badgeClass = criticalCount > 0 ? 'bg-danger' : highCount > 0 ? 'bg-warning' : 'bg-info';
            badgeHtml = `<span class="badge ${badgeClass}">${vulnCount} encontrada${vulnCount !== 1 ? 's' : ''}</span>`;
        }

        vulnBadge.innerHTML = badgeHtml;
    }
    
    updateVulnerabilities(vulnerabilities) {
        const vulnCard = document.getElementById('vulnerabilitiesCard');
        if (!vulnCard) return;
        
        if (vulnerabilities.length === 0) {
            vulnCard.innerHTML = `
                <div class="text-center text-success">
                    <i class="fas fa-shield-alt fa-3x mb-3"></i>
                    <h5>No se detectaron vulnerabilidades</h5>
                    <p class="text-muted">El análisis no encontró vulnerabilidades conocidas en este momento.</p>
                </div>
            `;
            return;
        }
        
        let html = '<div class="row">';
        
        // Estadisticas rápidas
        const severityCounts = this.countBySeverity(vulnerabilities);
        
        html += `
            <div class="col-12 mb-3">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="p-3 bg-danger text-white rounded">
                            <h4>${severityCounts.critical || 0}</h4>
                            <small>Críticas</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="p-3 bg-warning text-white rounded">
                            <h4>${severityCounts.high || 0}</h4>
                            <small>Altas</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="p-3 bg-info text-white rounded">
                            <h4>${severityCounts.medium || 0}</h4>
                            <small>Medias</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="p-3 bg-success text-white rounded">
                            <h4>${severityCounts.low || 0}</h4>
                            <small>Bajas</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Lista de vulnerabilidades
        html += '<div class="col-12">';
        vulnerabilities.forEach(vuln => {
            const severityClass = this.getSeverityClass(vuln.severity);
            const severityBadge = this.getSeverityBadge(vuln.severity);
            
            html += `
                <div class="vulnerability-item vulnerability-${vuln.severity} mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">
                                ${severityBadge}
                                ${this.escapeHtml(vuln.name)}
                            </h6>
                            <p class="mb-1 text-muted small">${this.escapeHtml(vuln.description)}</p>
                            <small class="text-muted">
                                <strong>Template:</strong> ${this.escapeHtml(vuln.template_id)}<br>
                                <strong>URL:</strong> ${this.escapeHtml(vuln.matched_at)}
                            </small>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-bug ${severityClass}"></i>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div></div>';
        
        vulnCard.innerHTML = html;
    }
    
    updateDNSInfo(toolsResults) {
        const dnsInfo = document.getElementById('dnsInfo');
        if (!dnsInfo) return;
        
        const digSummary = toolsResults?.dig_summary;
        
        if (!digSummary || digSummary.status !== 'completed') {
            dnsInfo.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p class="mt-2">Consultando DNS...</p>
                </div>
            `;
            return;
        }
        
        const dnsData = digSummary.output || {};
        let html = '';
        
        Object.entries(dnsData).forEach(([recordType, records]) => {
            html += `
                <div class="mb-3">
                    <h6><strong>${recordType} Records:</strong></h6>
            `;
            
            if (records && records.length > 0 && records[0] !== '') {
                html += '<ul class="list-unstyled ms-3">';
                records.forEach(record => {
                    if (record.trim()) {
                        html += `<li><code>${this.escapeHtml(record)}</code></li>`;
                    }
                });
                html += '</ul>';
            } else {
                html += '<p class="text-muted ms-3">No hay registros</p>';
            }
            
            html += '</div>';
        });
        
        dnsInfo.innerHTML = html || '<p class="text-muted">No se pudo obtener información DNS</p>';
    }
    
    updateWhoisInfo(toolsResults) {
        const whoisInfo = document.getElementById('whoisInfo');
        if (!whoisInfo) return;
        
        const whoisData = toolsResults?.whois;
        
        if (!whoisData || whoisData.status !== 'completed') {
            whoisInfo.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p class="mt-2">Consultando WHOIS...</p>
                </div>
            `;
            return;
        }
        
        const output = whoisData.output || '';
        if (output.trim()) {
            // Extraer información clave del WHOIS
            const lines = output.split('\n');
            const keyInfo = {};
            
            lines.forEach(line => {
                const lowerLine = line.toLowerCase();
                if (lowerLine.includes('registrar:')) {
                    keyInfo.registrar = line.split(':')[1]?.trim();
                } else if (lowerLine.includes('creation date:') || lowerLine.includes('created:')) {
                    keyInfo.created = line.split(':')[1]?.trim();
                } else if (lowerLine.includes('expiry date:') || lowerLine.includes('expires:')) {
                    keyInfo.expires = line.split(':')[1]?.trim();
                }
            });
            
            let html = '';
            
            if (Object.keys(keyInfo).length > 0) {
                html += '<div class="mb-3">';
                if (keyInfo.registrar) html += `<p><strong>Registrar:</strong> ${this.escapeHtml(keyInfo.registrar)}</p>`;
                if (keyInfo.created) html += `<p><strong>Creado:</strong> ${this.escapeHtml(keyInfo.created)}</p>`;
                if (keyInfo.expires) html += `<p><strong>Expira:</strong> ${this.escapeHtml(keyInfo.expires)}</p>`;
                html += '</div>';
            }
            
            html += `<details><summary>Ver información completa</summary><pre class="mt-2">${this.escapeHtml(output)}</pre></details>`;
            
            whoisInfo.innerHTML = html;
        } else {
            whoisInfo.innerHTML = '<p class="text-muted">No se pudo obtener información WHOIS</p>';
        }
    }
    
    updateSubdomains(toolsResults) {
        const subdomainsInfo = document.getElementById('subdomainsInfo');
        if (!subdomainsInfo) return;
        
        const assetfinderData = toolsResults?.assetfinder;
        
        if (!assetfinderData || assetfinderData.status !== 'completed') {
            subdomainsInfo.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p class="mt-2">Enumerando subdominios...</p>
                </div>
            `;
            return;
        }
        
        const output = assetfinderData.output || '';
        if (output.trim()) {
            const subdomains = output.split('\n').filter(line => line.trim()).slice(0, 20); // Primeros 20
            
            let html = `<p class="mb-2"><strong>Encontrados:</strong> ${subdomains.length} subdominios</p>`;
            html += '<ul class="list-unstyled">';
            
            subdomains.forEach(subdomain => {
                if (subdomain.trim()) {
                    html += `<li><i class="fas fa-globe text-info me-2"></i><code>${this.escapeHtml(subdomain.trim())}</code></li>`;
                }
            });
            
            html += '</ul>';
            subdomainsInfo.innerHTML = html;
        } else {
            subdomainsInfo.innerHTML = '<p class="text-muted">No se encontraron subdominios</p>';
        }
    }
    
    updateEmails(toolsResults) {
        const emailsInfo = document.getElementById('emailsInfo');
        if (!emailsInfo) return;
        
        const harvesterData = toolsResults?.theharvester;
        
        if (!harvesterData || harvesterData.status !== 'completed') {
            emailsInfo.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p class="mt-2">Recolectando emails...</p>
                </div>
            `;
            return;
        }
        
        // Primero verificar si hay emails en los resultados procesados
        const emailsFound = harvesterData.emails_found || [];
        const output = harvesterData.output || '';
        
        let emails = [];
        
        // Si hay emails procesados, usarlos
        if (emailsFound && emailsFound.length > 0) {
            emails = emailsFound;
        } else if (output.trim()) {
            // Si no, extraer emails del output usando regex
            const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
            emails = [...new Set(output.match(emailRegex) || [])];
        }
        
        if (emails.length > 0) {
            let html = `<p class="mb-2"><strong>Encontrados:</strong> ${emails.length} emails</p>`;
            html += '<ul class="list-unstyled">';
            
            emails.slice(0, 15).forEach(email => { // Primeros 15
                html += `<li><i class="fas fa-envelope text-success me-2"></i><code>${this.escapeHtml(email)}</code></li>`;
            });
            
            if (emails.length > 15) {
                html += `<li class="text-muted">... y ${emails.length - 15} más</li>`;
            }
            
            html += '</ul>';
            emailsInfo.innerHTML = html;
        } else {
            emailsInfo.innerHTML = '<p class="text-muted">No se encontraron emails públicos</p>';
        }
    }
    
    updateRecommendations(recommendations) {
        const recommendationsCard = document.getElementById('recommendationsCard');
        if (!recommendationsCard) return;
        
        if (recommendations.length === 0) {
            recommendationsCard.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-lightbulb fa-2x mb-3"></i>
                    <p>Generando recomendaciones personalizadas...</p>
                </div>
            `;
            return;
        }
        
        let html = '<div class="list-group list-group-flush">';
        
        recommendations.forEach(rec => {
            const iconClass = this.getRecommendationIcon(rec.type);
            const badgeClass = this.getRecommendationBadge(rec.type);
            
            html += `
                <div class="list-group-item border-0 px-0">
                    <div class="d-flex align-items-start">
                        <div class="me-3">
                            <i class="${iconClass}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start">
                                <p class="mb-0">${this.escapeHtml(rec.message)}</p>
                                <span class="${badgeClass} ms-2">${rec.type.toUpperCase()}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        recommendationsCard.innerHTML = html;
    }
    
    onScanCompleted(data) {
        const startBtn = document.getElementById('startScanBtn');
        const btnText = startBtn?.querySelector('.btn-text');
        const btnLoader = startBtn?.querySelector('.btn-loader');
        const downloadBtn = document.getElementById('downloadReportBtn');
        const currentTool = document.getElementById('currentTool');

        // Reset scan button to normal state
        if (startBtn) {
            startBtn.disabled = false;
            if (btnText && btnLoader) {
                btnText.classList.remove('d-none');
                btnLoader.classList.add('d-none');
            } else {
                startBtn.innerHTML = '<i class="fas fa-play me-2"></i><span>Iniciar Análisis Completo</span>';
            }
        }

        // Show download button with animation
        if (downloadBtn && (data.status === 'completed' || data.status === 'completed_with_errors')) {
            downloadBtn.style.display = 'inline-block';
            downloadBtn.classList.add('bounce-in');
        }

        // Enhanced completion messages with notifications
        const vulnCount = data.vulnerabilities ? data.vulnerabilities.length : 0;
        const criticalCount = data.vulnerabilities ? data.vulnerabilities.filter(v => v.severity === 'critical').length : 0;

        if (data.status === 'completed') {
            if (vulnCount > 0) {
                const message = criticalCount > 0
                    ? `Análisis completado: ${vulnCount} vulnerabilidades encontradas (${criticalCount} críticas)`
                    : `Análisis completado: ${vulnCount} vulnerabilidades encontradas`;
                this.showNotification(message, criticalCount > 0 ? 'warning' : 'info');
            } else {
                this.showNotification('Análisis completado: No se encontraron vulnerabilidades', 'success');
            }
        } else if (data.status === 'completed_with_errors') {
            this.showNotification('Análisis completado con algunos errores en las herramientas', 'warning');
        } else {
            this.showNotification('Error crítico en el análisis', 'danger');
        }

        // Update system status
        this.updateSystemStatus('completed');

        // Play completion sound (if enabled)
        this.playCompletionSound();

        // Show completion animation
        this.showCompletionAnimation(data.status);
    }

    updateSystemStatus(status) {
        const systemStatus = document.getElementById('systemStatus');
        if (!systemStatus) return;

        const statusConfig = {
            'active': { icon: 'fa-circle text-success pulse', text: 'Sistema Activo' },
            'scanning': { icon: 'fa-circle text-warning pulse', text: 'Escaneando...' },
            'completed': { icon: 'fa-check-circle text-success', text: 'Escaneo Completo' },
            'error': { icon: 'fa-exclamation-circle text-danger', text: 'Error' }
        };

        const config = statusConfig[status] || statusConfig['active'];
        systemStatus.innerHTML = `
            <i class="fas ${config.icon}"></i>
            <span class="ms-1">${config.text}</span>
        `;
    }

    playCompletionSound() {
        // Only play if user has interacted with the page (browser policy)
        if (document.hasFocus()) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.2);
            } catch (e) {
                // Audio not supported or blocked
                console.log('Audio notification not available');
            }
        }
    }

    showCompletionAnimation(status) {
        // Create a temporary celebration element
        if (status === 'completed') {
            const celebration = document.createElement('div');
            celebration.className = 'completion-celebration';
            celebration.innerHTML = '<i class="fas fa-check-circle fa-3x text-success"></i>';
            celebration.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 9999;
                animation: celebration 2s ease-out forwards;
                pointer-events: none;
            `;

            document.body.appendChild(celebration);

            setTimeout(() => {
                celebration.remove();
            }, 2000);
        }
    }
    
    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }
    
    async downloadReport() {
        if (!this.currentScanId) {
            this.showAlert('No hay reporte disponible para descargar', 'warning');
            return;
        }
        
        try {
            const response = await fetch(`/report/${this.currentScanId}`);
            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Error al generar el reporte');
            }
            
            // Descargar archivo
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `security_report_${this.currentScanId}.html`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            this.showAlert('Reporte descargado exitosamente', 'success');
            
        } catch (error) {
            console.error('Error downloading report:', error);
            this.showAlert('Error al descargar el reporte: ' + error.message, 'danger');
        }
    }
    
    showInitialMessage() {
        const initialMessage = document.getElementById('initialMessage');
        const resultsSection = document.getElementById('resultsSection');
        
        if (initialMessage) initialMessage.style.display = 'block';
        if (resultsSection) resultsSection.style.display = 'none';
    }
    
    showAlert(message, type = 'info') {
        // Crear alerta temporal
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas ${
                    type === 'success' ? 'fa-check-circle' :
                    type === 'danger' ? 'fa-exclamation-circle' :
                    type === 'warning' ? 'fa-exclamation-triangle' :
                    'fa-info-circle'
                }"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // Insertar alerta al inicio del container
        const container = document.querySelector('.container-fluid');
        if (container) {
            const alertDiv = document.createElement('div');
            alertDiv.innerHTML = alertHtml;
            container.insertBefore(alertDiv.firstElementChild, container.firstChild);
            
            // Auto-remover después de 5 segundos
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => alert.remove(), 300);
                }
            }, 5000);
        }
    }
    
    // Funciones utilitarias
    countBySeverity(vulnerabilities) {
        return vulnerabilities.reduce((acc, vuln) => {
            acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;
            return acc;
        }, {});
    }
    
    getSeverityClass(severity) {
        const classes = {
            'critical': 'text-danger',
            'high': 'text-warning',
            'medium': 'text-info',
            'low': 'text-success'
        };
        return classes[severity] || 'text-secondary';
    }
    
    getSeverityBadge(severity) {
        const badges = {
            'critical': '<span class="badge bg-danger">CRÍTICA</span>',
            'high': '<span class="badge bg-warning">ALTA</span>',
            'medium': '<span class="badge bg-info">MEDIA</span>',
            'low': '<span class="badge bg-success">BAJA</span>'
        };
        return badges[severity] || `<span class="badge bg-secondary">${severity.toUpperCase()}</span>`;
    }
    
    getRecommendationIcon(type) {
        const icons = {
            'critical': 'fas fa-exclamation-circle text-danger',
            'warning': 'fas fa-exclamation-triangle text-warning',
            'info': 'fas fa-info-circle text-info'
        };
        return icons[type] || 'fas fa-lightbulb text-primary';
    }
    
    getRecommendationBadge(type) {
        const badges = {
            'critical': 'badge bg-danger',
            'warning': 'badge bg-warning',
            'info': 'badge bg-info'
        };
        return badges[type] || 'badge bg-primary';
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Inicializar aplicación cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    window.securityScannerApp = new SecurityScannerApp();
});

